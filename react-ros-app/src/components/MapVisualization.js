import React, { useContext, useEffect, useRef, useState } from 'react';
import ROSLIB from 'roslib';
import { RosContext } from './RosConnection';
import './MapVisualization.css';

const MapVisualization = () => {
  const { ros, isConnected } = useContext(RosContext);
  const canvasRef = useRef(null);
  const filteredOdomSubscriberRef = useRef(null);
  const animationFrameRef = useRef(null);
  const [mapStatus, setMapStatus] = useState('Initializing...');
  const [robotPosition, setRobotPosition] = useState({ x: 0, y: 0, theta: 0 });
  const [showAxes, setShowAxes] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [showPath, setShowPath] = useState(true);
  const [scaleFactor, setScaleFactor] = useState(30); // Scale factor for visualization
  const [pathPoints, setPathPoints] = useState([]); // Store the robot's path

  // Function to draw the map and robot
  const drawMap = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear the canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    // Draw grid
    if (showGrid) {
      ctx.strokeStyle = '#cccccc';
      ctx.lineWidth = 0.5;

      // Draw vertical grid lines
      const gridSize = 20; // pixels per grid cell
      const gridMeters = 1; // 1 meter per grid cell
      const numCellsX = Math.floor(canvas.width / gridSize) + 1;
      const numCellsY = Math.floor(canvas.height / gridSize) + 1;

      // Calculate offset to center the grid
      const offsetX = centerX % gridSize;
      const offsetY = centerY % gridSize;

      // Draw vertical grid lines
      for (let i = 0; i < numCellsX; i++) {
        const x = i * gridSize + offsetX;
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();

        // Draw meter labels on x-axis
        if (i % 5 === 0) { // Label every 5 meters
          const meterValue = ((i * gridSize + offsetX) - centerX) / scaleFactor;
          ctx.fillStyle = '#888888';
          ctx.font = '10px Arial';
          ctx.textAlign = 'center';
          ctx.fillText(`${meterValue.toFixed(0)}m`, x, centerY + 15);
        }
      }

      // Draw horizontal grid lines
      for (let i = 0; i < numCellsY; i++) {
        const y = i * gridSize + offsetY;
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();

        // Draw meter labels on y-axis
        if (i % 5 === 0) { // Label every 5 meters
          const meterValue = -((i * gridSize + offsetY) - centerY) / scaleFactor; // Negate for proper orientation
          ctx.fillStyle = '#888888';
          ctx.font = '10px Arial';
          ctx.textAlign = 'right';
          ctx.fillText(`${meterValue.toFixed(0)}m`, centerX - 10, y + 4);
        }
      }
    }

    // Draw coordinate axes
    if (showAxes) {
      // X-axis
      ctx.strokeStyle = '#ff0000';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(centerX + 50, centerY);
      ctx.stroke();

      // Add X label
      ctx.fillStyle = '#ff0000';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('X', centerX + 60, centerY + 4);

      // Y-axis
      ctx.strokeStyle = '#00ff00';
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(centerX, centerY - 50);
      ctx.stroke();

      // Add Y label
      ctx.fillStyle = '#00ff00';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Y', centerX, centerY - 60);
    }

    // Draw robot path
    if (showPath && pathPoints.length > 1) {
      ctx.strokeStyle = '#e74c3c'; // Red path
      ctx.lineWidth = 2;
      ctx.beginPath();

      // Start from the first point
      const firstPoint = pathPoints[0];
      const firstX = centerX + firstPoint.x * scaleFactor;
      const firstY = centerY - firstPoint.y * scaleFactor; // Negate Y for proper orientation

      ctx.moveTo(firstX, firstY);

      // Draw lines to each subsequent point
      for (let i = 1; i < pathPoints.length; i++) {
        const point = pathPoints[i];
        const x = centerX + point.x * scaleFactor;
        const y = centerY - point.y * scaleFactor; // Negate Y for proper orientation
        ctx.lineTo(x, y);
      }

      ctx.stroke();
    }

    // Draw robot
    if (robotPosition) {
      // Calculate robot position on canvas
      const robotX = centerX + robotPosition.x * scaleFactor;
      const robotY = centerY - robotPosition.y * scaleFactor; // Negate Y for proper orientation

      // Save the current context state
      ctx.save();

      // Translate to robot position and rotate
      ctx.translate(robotX, robotY);
      ctx.rotate(-robotPosition.theta); // Negate for proper orientation

      // Draw robot body (rectangle with rounded corners for a four-wheeled robot)
      const robotWidth = 25;
      const robotHeight = 35;

      // Draw robot base (rectangle)
      ctx.fillStyle = '#3498db';
      ctx.strokeStyle = '#2980b9';
      ctx.lineWidth = 2;

      // Rounded rectangle for robot body
      ctx.beginPath();
      ctx.roundRect(-robotWidth/2, -robotHeight/2, robotWidth, robotHeight, 5);
      ctx.fill();
      ctx.stroke();

      // Draw wheels (black rectangles) - Four wheels for a four-wheeled robot
      ctx.fillStyle = '#333333';

      // Front-left wheel
      ctx.fillRect(-robotWidth/2 - 2, -robotHeight/2 + 5, 4, 8);

      // Front-right wheel
      ctx.fillRect(robotWidth/2 - 2, -robotHeight/2 + 5, 4, 8);

      // Rear-left wheel
      ctx.fillRect(-robotWidth/2 - 2, robotHeight/2 - 13, 4, 8);

      // Rear-right wheel
      ctx.fillRect(robotWidth/2 - 2, robotHeight/2 - 13, 4, 8);

      // Draw front indicator (triangle)
      ctx.fillStyle = '#e74c3c';
      ctx.beginPath();
      ctx.moveTo(0, -robotHeight/2 - 5);
      ctx.lineTo(-5, -robotHeight/2);
      ctx.lineTo(5, -robotHeight/2);
      ctx.closePath();
      ctx.fill();

      // Draw sensor/camera (small circle)
      ctx.fillStyle = '#2c3e50';
      ctx.beginPath();
      ctx.arc(0, -robotHeight/2 + 5, 3, 0, Math.PI * 2);
      ctx.fill();

      // Restore the context
      ctx.restore();

      // Draw robot label
      ctx.fillStyle = '#3498db';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Robot', robotX, robotY - 25);
    }
  };

  // Animation loop for continuous rendering
  useEffect(() => {
    const animate = () => {
      drawMap();
      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animationFrameRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [robotPosition, showGrid, showAxes, scaleFactor]);

  // Subscribe to filtered odometry
  useEffect(() => {
    if (!isConnected || !ros) {
      setMapStatus('Waiting for ROS connection...');
      return;
    }

    setMapStatus('Connected to ROS. Waiting for odometry data...');

    // Subscribe to filtered odometry
    filteredOdomSubscriberRef.current = new ROSLIB.Topic({
      ros: ros,
      name: '/odometry/filtered',
      messageType: 'nav_msgs/Odometry'
    });

    filteredOdomSubscriberRef.current.subscribe((message) => {
      // Extract position and orientation
      const position = message.pose.pose.position;
      const orientation = message.pose.pose.orientation;

      // Calculate yaw from quaternion
      const quaternion = new ROSLIB.Quaternion(orientation);
      const yaw = Math.atan2(
        2.0 * (quaternion.w * quaternion.z + quaternion.x * quaternion.y),
        1.0 - 2.0 * (quaternion.y * quaternion.y + quaternion.z * quaternion.z)
      );

      // Update robot position
      const newPosition = {
        x: position.x,
        y: position.y,
        theta: yaw
      };

      setRobotPosition(newPosition);

      // Add point to path (but not too many points to avoid performance issues)
      setPathPoints(prevPoints => {
        // Only add a point if it's significantly different from the last one
        const lastPoint = prevPoints.length > 0 ? prevPoints[prevPoints.length - 1] : null;

        if (!lastPoint ||
            Math.sqrt(Math.pow(lastPoint.x - position.x, 2) +
                     Math.pow(lastPoint.y - position.y, 2)) > 0.05) {

          // Limit the number of points to prevent memory issues
          const maxPoints = 500;
          const newPoints = [...prevPoints, { x: position.x, y: position.y }];

          if (newPoints.length > maxPoints) {
            return newPoints.slice(newPoints.length - maxPoints);
          }

          return newPoints;
        }

        return prevPoints;
      });

      setMapStatus('Receiving filtered odometry data');
    });

    // Clean up on unmount
    return () => {
      if (filteredOdomSubscriberRef.current) {
        filteredOdomSubscriberRef.current.unsubscribe();
      }
    };
  }, [ros, isConnected]);

  // Function to adjust scale
  const handleScaleChange = (newScale) => {
    setScaleFactor(newScale);
  };

  return (
    <div className="map-container">
      <h2>Robot Position Visualization</h2>
      <div className="map-status">{mapStatus}</div>

      <div className="map-controls">
        <div className="control-group">
          <label>
            <input
              type="checkbox"
              checked={showGrid}
              onChange={() => setShowGrid(!showGrid)}
            />
            Show Grid
          </label>

          <label>
            <input
              type="checkbox"
              checked={showAxes}
              onChange={() => setShowAxes(!showAxes)}
            />
            Show Axes
          </label>

          <label>
            <input
              type="checkbox"
              checked={showPath}
              onChange={() => setShowPath(!showPath)}
            />
            Show Path
          </label>
        </div>

        <div className="control-group">
          <label>
            Scale:
            <input
              type="range"
              min="10"
              max="100"
              value={scaleFactor}
              onChange={(e) => handleScaleChange(parseInt(e.target.value))}
            />
            {scaleFactor}
          </label>

          <button
            className="clear-path-btn"
            onClick={() => setPathPoints([])}
          >
            Clear Path
          </button>
        </div>
      </div>

      <canvas
        ref={canvasRef}
        width={600}
        height={400}
        className="map-canvas"
      />

      <div className="position-info">
        <h3>Filtered Odometry Data</h3>
        <p>
          <strong>Position:</strong> X: {robotPosition.x.toFixed(3)} m, Y: {robotPosition.y.toFixed(3)} m
        </p>
        <p>
          <strong>Orientation:</strong> {(robotPosition.theta * 180 / Math.PI).toFixed(2)}°
          ({robotPosition.theta.toFixed(3)} rad)
        </p>
        <p>
          <strong>Path Length:</strong> {pathPoints.length} points
        </p>
      </div>

      <div className="map-instructions">
        <p>This visualization shows the robot's position based on filtered odometry data.</p>
        <p>Use the joystick to move the robot and see its position update in real-time.</p>
        <p>The red path shows the robot's trajectory as it moves.</p>
        <p>Adjust the scale slider to zoom in or out of the map.</p>
      </div>
    </div>
  );
};

export default MapVisualization;
