import React, { useState } from 'react';
import './App.css';
import RosConnection from './components/RosConnection';
import JoystickControl from './components/JoystickControl';
import MapVisualization from './components/MapVisualization';
import RobotStatus from './components/RobotStatus';

function App() {
  const [rosUrl, setRosUrl] = useState('ws://localhost:9090');
  const [isConfiguring, setIsConfiguring] = useState(true);

  const handleConnect = (e) => {
    e.preventDefault();
    setIsConfiguring(false);
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>ROS Robot Control Interface</h1>
      </header>

      {isConfiguring ? (
        <div className="connection-form">
          <h2>Connect to ROS Bridge Server</h2>
          <form onSubmit={handleConnect}>
            <div className="form-group">
              <label htmlFor="rosUrl">ROS Bridge URL:</label>
              <input
                type="text"
                id="rosUrl"
                value={rosUrl}
                onChange={(e) => setRosUrl(e.target.value)}
                placeholder="ws://localhost:9090"
              />
            </div>
            <button type="submit">Connect</button>
          </form>
        </div>
      ) : (
        <RosConnection url={rosUrl}>
          <div className="connection-status">
            <button onClick={() => setIsConfiguring(true)}>
              Change Connection
            </button>
          </div>
          <div className="dashboard">
            <div className="dashboard-column">
              <JoystickControl />
              <RobotStatus />
            </div>
            <div className="dashboard-column">
              <MapVisualization />
            </div>
          </div>
        </RosConnection>
      )}
    </div>
  );
}

export default App;
