# ROS Robot Control Interface

A web-based UI built with React.js to control and monitor a robot running ROS Noetic. This application uses rosbridge, roslibjs, and Robot Web Tools to communicate with the ROS system.

## Features

- **Manual Robot Control**: Control the robot's movement using an on-screen joystick that sends Twist messages to the `/cmd_vel` topic.
- **Robot Position Visualization**: Display the robot's current position on a 2D map using data from `/odom`, `/odometry/filtered`, and TF.
- **Robot Status Display**: Monitor the robot's current velocity, position, and orientation in real-time.

## Prerequisites

- ROS Noetic installed on the robot
- rosbridge_server package installed and running
- A web browser with JavaScript enabled

## Installation

1. Install rosbridge_server on your ROS system if you haven't already:
   ```
   sudo apt-get install ros-noetic-rosbridge-server
   ```

2. Install dependencies for this React application:
   ```
   cd react-ros-app
   npm install
   ```

## Usage

1. Start the ROS system on your robot:
   ```
   roslaunch rosbridge_server rosbridge_websocket.launch
   ```

2. Start the React development server:
   ```
   npm start
   ```

3. Open the web application in your browser (it should open automatically at http://localhost:3000).

4. Enter the WebSocket URL of your rosbridge server (default: `ws://localhost:9090`).

5. Use the on-screen joystick to control the robot:
   - Move up/down for forward/backward movement
   - Move left/right for rotation

6. Monitor the robot's position on the 2D map and check its status in the status panel.

## ROS Topics Used

- `/cmd_vel` (geometry_msgs/Twist): For sending velocity commands to the robot
- `/odom` (nav_msgs/Odometry): For receiving odometry data
- `/odometry/filtered` (nav_msgs/Odometry): For receiving EKF-based localization data

## Customization

You can customize the application by:

- Adjusting the maximum linear and angular velocities in the `JoystickControl.js` file
- Modifying the map visualization settings in the `MapVisualization.js` file
- Adding additional ROS topic subscriptions in the relevant components

## Troubleshooting

- If you cannot connect to the rosbridge server, make sure it's running and accessible from your browser.
- If the robot doesn't move when using the joystick, check if the `/cmd_vel` topic is correctly set up on your robot.
- If the map visualization doesn't show the robot's position, verify that the odometry topics are publishing data.

## Available Scripts

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

### `npm test`

Launches the test runner in the interactive watch mode.

### `npm run build`

Builds the app for production to the `build` folder.
