<launch>

 
  
     <include file="$(find realsense2_camera)/launch/rs_camera.launch">
        <!-- Camera resolution options -->
        <arg name="camera" default="camera" />
        <arg name="serial_no" default=""/> <!-- Specify your camera's serial number if needed -->
        
        <arg name="enable_color" default="true"/> 
    	<arg name="enable_depth" default="false"/>
    	<arg name="enable_infra1" default="false"/>
    	<arg name="enable_infra2" default="false"/>
    	<arg name="align_depth" default="false"/>
	<arg name="enable_pointcloud" default="false"/>
	<arg name="enable_sync" default="false"/>
        
        <arg name="color_width" default="640"/>
        <arg name="color_height" default="480"/>
    	<arg name="color_fps" default="30"/>
    	
    </include>
    
    <node pkg="visual_servoing" type="row_crop_follower.py" name="row_crop_follower" output="screen" launch-prefix="python3"/>


 
   <!-- Launch other nodes -->
  <!-- <include file="$(find robot_description)/launch/controller.launch" /> -->



  
</launch>
